package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import javax.validation.constraints.NotNull;
import java.util.List;

@JsonRootName("Transfer Market Bid User Response")
@Getter
@Setter
@Schema(description = "Transfer market bid user info Response")
public class TransferMarketBidUserResponse {

    @NotNull
    @Schema(description = "List of transferMarketBidUserDto")
    private List<TransferMarketBidUserDto> transferMarketBidUserDtoList;

    @NotNull
    @Schema(description = "hase history or no", required = true)
    private boolean hasHistory;

    public TransferMarketBidUserResponse(List<TransferMarketBidUserDto> transferMarketBidUserDtoList, boolean hasHistory) {
        this.transferMarketBidUserDtoList = transferMarketBidUserDtoList;
        this.hasHistory = hasHistory;
    }
}
