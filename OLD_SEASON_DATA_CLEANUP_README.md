# Old Season Data Cleanup Implementation

This document provides instructions for using the newly implemented old season data cleanup endpoints.

## Overview

The implementation provides two endpoints for safely cleaning up data from the last previous season:

1. **Dry Run Endpoint**: Preview what would be deleted without performing actual deletions
2. **Cleanup Endpoint**: Perform actual deletion of old season data

**Important**: The cleanup only targets the **last previous season** that ended before the configured buffer period (default: 15 days).

## Prerequisites

### 1. Database Parameter Setup

Before using the endpoints, you must configure the secret key in the database:

```sql
-- Run this SQL to set up the required parameters
INSERT INTO admin.parameter (id, name, value, created_at, modified_at, deleted, deleted_at)
VALUES (
    gen_random_uuid(),
    'deleteOldDataSecretKey',
    'your-secret-key',  -- Replace with a strong secret key
    NOW(),
    NOW(),
    false,
    NULL
)
ON CONFLICT (name) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    modified_at = NOW();

-- Optional: Configure buffer days (defaults to ParameterDefaults value: 15)
INSERT INTO admin.parameter (id, name, value, created_at, modified_at, deleted, deleted_at)
VALUES (
    gen_random_uuid(),
    'cleanupSeasonEndBufferDays',
    '15',  -- Number of days after season end before cleanup is allowed
    NOW(),
    NOW(),
    false,
    NULL
)
ON CONFLICT (name) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    modified_at = NOW();

-- Optional: Configure batch wait time (defaults to ParameterDefaults value: 1000ms)
INSERT INTO admin.parameter (id, name, value, created_at, modified_at, deleted, deleted_at)
VALUES (
    gen_random_uuid(),
    'cleanupBatchWaitTimeMs',
    '1000',  -- Wait time between batches in milliseconds
    NOW(),
    NOW(),
    false,
    NULL
)
ON CONFLICT (name) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    modified_at = NOW();
```

### 2. Admin Role Required

Both endpoints require `ROLE_ADMIN` authentication.

## API Endpoints

### 1. Dry Run Endpoint

**GET** `/v1/admin/data-cleanup/delete-old-season-data/dry-run`

**Parameters:**
- `secretKey` (required): The secret key configured in the database

**Example:**
```bash
curl -X GET "http://localhost:8181/adminapi/v1/admin/data-cleanup/delete-old-season-data/dry-run?secretKey=your-secret-key" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**Response:**
```json
{
  "success": true,
  "message": "Dry run completed - last previous season found",
  "deletedRecords": {
    "transfer_market_bid": 4250000,
    "transfer_market": 85000,
    "lineup": 28000,
    "user_round_score": 35000,
    "team": 42000,
    "league_invitation": 450,
    "user_notification": 12500,
    "scheduler_job": 120
  },
  "totalDeleted": 4441110,
  "executionTime": "00:00"
}
```

### 2. Cleanup Endpoint

**POST** `/v1/admin/data-cleanup/delete-old-season-data`

**Request Body:**
```json
{
  "secretKey": "your-secret-key",
  "batchSize": 10000,
  "waitTimeBetweenBatchesMs": 100
}
```

**Parameters:**
- `secretKey` (required): The secret key configured in the database
- `batchSize` (optional): Number of records to process per batch (default: 10000, minimum: 1000)
- `waitTimeBetweenBatchesMs` (optional): Wait time between batches in milliseconds (default: 100)

**Example:**
```bash
curl -X POST "http://localhost:8181/adminapi/v1/admin/data-cleanup/delete-old-season-data" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "secretKey": "your-secret-key",
    "batchSize": 5000,
    "waitTimeBetweenBatchesMs": 200
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Old season data cleanup completed successfully for the last previous season",
  "deletedRecords": {
    "transfer_market_bid": 4250000,
    "transfer_market": 85000,
    "lineup": 28000,
    "user_round_score": 35000,
    "team": 42000,
    "league_invitation": 450,
    "user_notification": 12500,
    "scheduler_job": 120
  },
  "totalDeleted": 4441110,
  "executionTime": "00:25:30"
}
```

## Safety Features

### 1. Last Previous Season Only
- Only deletes data from the **last previous season** that ended before the buffer period
- Buffer period is configurable (default: 15 days after season end)
- Current season data is protected from accidental deletion
- Uses season end date to determine eligibility

### 2. Secret Key Security
- Requires a secret key stored in the database
- Prevents unauthorized access to cleanup operations
- Key validation occurs before any operations

### 3. Batch Processing
- Processes large tables in configurable batches
- Configurable wait time between batches to reduce database load
- Proper transaction management to avoid timeouts

### 4. Dry Run Capability
- Preview what would be deleted before actual cleanup
- Returns exact counts for each table
- No actual deletions performed in dry run mode

## Tables Cleaned

The cleanup process handles these tables in dependency order:

1. `transfer_market_bid` - Transfer market bids
2. `transfer_market` - Transfer market offers  
3. `lineup` - Team lineups for rounds
4. `user_round_score` - User scores per round
5. `team` - User team compositions
6. `league_invitation` - League invitations
7. `user_notification` - User notifications
8. `scheduler_job` - Scheduled job records

## Error Handling

### Common Error Responses

**403 Forbidden - Invalid Secret Key:**
```json
{
  "success": false,
  "message": "Invalid secret key",
  "deletedRecords": {},
  "totalDeleted": 0,
  "executionTime": "00:00"
}
```

**400 Bad Request - Invalid Parameters:**
```json
{
  "success": false,
  "message": "Batch size must be at least 1000",
  "deletedRecords": {},
  "totalDeleted": 0,
  "executionTime": "00:00"
}
```

## Configuration

The cleanup service uses `ParameterService` to access configuration values with fallback to `ParameterDefaults.java`:

```java
// Default values defined in ParameterDefaults.java
String PARAM_CLEANUP_BATCH_WAIT_TIME_MS = "cleanupBatchWaitTimeMs";
long DEFAULT_CLEANUP_BATCH_WAIT_TIME_MS = 1000;  // 1 second between batches

String PARAM_CLEANUP_SEASON_END_BUFFER_DAYS = "cleanupSeasonEndBufferDays";
int DEFAULT_CLEANUP_SEASON_END_BUFFER_DAYS = 15;  // 15 days after season end
```

The service uses `ParameterService.getValue()` and `ParameterService.getAsInteger()` to retrieve values from the `admin.parameter` table, with automatic fallback to `ParameterDefaults` if not found.

## Monitoring

- All operations are logged with appropriate log levels
- Progress is tracked and reported during batch processing
- Execution time is measured and returned in responses
- Error details are logged for troubleshooting

## Best Practices

1. **Always run dry run first** to preview what will be deleted
2. **Use appropriate batch sizes** based on your database performance
3. **Monitor database performance** during cleanup operations
4. **Schedule cleanup during low-traffic periods**
5. **Keep the secret key secure** and rotate it periodically
6. **Create database backups** before running cleanup operations
