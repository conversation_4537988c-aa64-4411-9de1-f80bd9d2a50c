package com.pass.hbl.manager.backend.persistence.dto.admin;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * Request DTO for old season data cleanup operations
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataCleanupRequest {

    /**
     * Secret key required for authorization
     */
    @NotBlank(message = "Secret key is required")
    private String secretKey;

    /**
     * Batch size for processing large tables (default: 10000)
     */
    @Min(value = 1000, message = "Batch size must be at least 1000")
    private int batchSize = 10000;

    /**
     * Wait time between batches in milliseconds (default: 100ms)
     */
    @Min(value = 0, message = "Wait time cannot be negative")
    private long waitTimeBetweenBatchesMs = 100;

    /**
     * Validates the request parameters
     */
    public boolean isValid() {
        return StringUtils.isNotBlank(secretKey) && 
               batchSize >= 1000 && 
               waitTimeBetweenBatchesMs >= 0;
    }

    @Override
    public String toString() {
        return "DataCleanupRequest{" +
                "secretKey='***'" +
                ", batchSize=" + batchSize +
                ", waitTimeBetweenBatchesMs=" + waitTimeBetweenBatchesMs +
                '}';
    }
}
