package com.pass.hbl.manager.backend.persistence.util;

import java.time.LocalDateTime;

public interface ParameterDefaults {

    String SYSTEM_USER = "system";

    String PARAM_DATACORE_COMPETITION_KEY = "DatacoreCompetitionId";
    String DEFAULT_DATACORE_COMPETITION_KEY = "349c9db2-9dd5-11ee-a7a8-b363dba6d9d2";
    String PARAM_DATACORE_PROD_COMPETITION_KEY = "DatacoreProdCompetitionId";
    String DEFAULT_DATACORE_PROD_COMPETITION_KEY = "4c445e5c-3956-11ef-9d0e-b74f5c057367";

    String PARAM_DATACORE_ORGANIZATION_KEY = "DatacoreOganizationId";
    String DEFAULT_DATACORE_ORGANIZATION_KEY = "h1s44";

    String PARAM_DATACORE_SPORT = "sport";
    String DEFAULT_DATACORE_SPORT = "handball";


    String PARAM_SPORTRADAR_COMPETITION_KEY = "SportradarCompetitionId";
    String DEFAULT_SPORTRADAR_COMPETITION_KEY = "sr:competition:149";

    String PARAM_SPORTRADAR_LOCALE = "SportradarLocale";
    String DEFAULT_SPORTRADAR_LOCALE = "de";

    String PARAM_LEAGUE_MAX_SIZE = "LeagueMaxSize";
    int DEFAULT_LEAGUE_MAX_SIZE = 12;

    String PARAM_MAX_REGISTRATION_DURATION = "MaxRegistrationDuration";
    String DEFAULT_MAX_REGISTRATION_DURATION = "7";

    String PARAM_INITIAL_BUDGET = "InitialBudget";
    String DEFAULT_INITIAL_BUDGET = "3000000";

    String PARAM_MAX_ROWS_DELETED = "MaxRowsDeleted";
    int  DEFAULT_MAX_ROWS_DELETED= 50000;

    String PARAM_INITIAL_TEAM_SETUP_BUDGET = "InitialTeamSetupBudget";
    String DEFAULT_INITIAL_TEAM_SETUP_BUDGET = "3000000";

    String PARAM_INITIAL_SUPERSTAR_CAP = "InitialSuperstarCap";
    String DEFAULT_INITIAL_SUPERSTAR_CAP = "1000000";

    String PARAM_INITIAL_TEAM_SIZE = "InitialTeamSize";
    int DEFAULT_INITIAL_TEAM_SIZE = 10;

    String PARAM_MAX_TEAM_SIZE = "MaxTeamSize";
    int DEFAULT_MAX_TEAM_SIZE = 18;

    String PARAM_MIN_FREE_PLAYERS_ON_MARKET = "MinFreePlayersOnMarket";
    int DEFAULT_MIN_FREE_PLAYERS_ON_MARKET = 80;

    String PARAM_LOWEST_BID_VALUE = "LowestBidValue";
    String DEFAULT_LOWEST_BID_VALUE = "10000";

    String PARAM_LOWEST_MARKET_VALUE = "LowestMarketValue";
    String DEFAULT_LOWEST_MARKET_VALUE = "10000";

    String PARAM_SEND_ROUND_RESULT_MESSAGE = "SendRoundResultMessage";

    Boolean DEFAULT_SEND_ROUND_RESULT_MESSAGE = true;

    String PARAM_RATE_LIMITING_ACTIVE ="rateLimitingActive";
    Boolean DEFAULT_PARAM_RATE_LIMITING_ACTIVE = false;

    String PARAM_STREAMING_ACTIVE = "StreamingActive";
    String PARAM_LIVE_TICKER_STREAMING_ACTIVE = "LiveTickerStreamingActive";
    String PARAM_USER_RANKING_STREAMING_ACTIVE = "UserRankingStreamingActive";

    String DEFAULT_STREAMING_ACTIVE = "true";
    String DEFAULT_LIVE_TICKER_STREAMING_ACTIVE = "true";
    String DEFAULT_USER_RANKING_STREAMING_ACTIVE = "true";

    String PARAM_STREAMING_TIMESTAMP = "StreamingTimestamp";

    String DEFAULT_STREAMING_TIMESTAMP = "null";

    String PARAM_START_DATE_SUMMER_BREAK = "StartDateSummerBreak";

    LocalDateTime DEFAULT_START_DATE_SUMMER_BREAK = LocalDateTime.of(2023, 6, 11, 16, 0, 0);

    String PARAM_END_DATE_SUMMER_BREAK = "EndDateSummerBreak";

    LocalDateTime DEFAULT_END_DATE_SUMMER_BREAK = LocalDateTime.of(2023, 9, 1, 0, 0, 0);

    String PARAM_TRANSFER_MARKET_ACTIVE = "TransferMarketActive";

    String DEFAULT_TRANSFER_MARKET_ACTIVE = "true";

    String PARAM_TRANSFER_MARKET_INACTIVE_REASON = "TransferMarketInactiveReason";

    String DEFAULT_TRANSFER_MARKET_INACTIVE_REASON = "DEFAULT";

    String PARAM_DATACORE_MQTT_STREAMING_ACTIVE = "dataCoreMqttStreamingActive";
    Boolean DEFAULT_DATACORE_MQTT_STREAMING_ACTIVE = false;

    String PARAM_MAX_STAT_ACTIVITY_PAGE_SIZE = "maxStatActivityPageSize";
    int DEFAULT_MAX_STAT_ACTIVITY_PAGE_SIZE = 1000;
    String PARAM_MAX_STAT_PAGE_SIZE = "maxStatPageSize";
    int DEFAULT_MAX_STAT_PAGE_SIZE = 1000;
    String PARAM_STAT_RATE_LIMIT_MINUTES = "maxStatLimitMinutes";
    int DEFAULT_PARAM_STAT_RATE_LIMIT_MINUTES = 30;
    String PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES = "forceRefreshCacheAfterMinutes";
    int DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES = 15;
    String PARAM_STAT_RATE_LIMITING_ACTIVE ="statRateLimitingActive";
    boolean DEFAULT_PARAM_STAT_RATE_LIMITING_ACTIVE = false;

    // Parameters for league statistics
    String PARAM_MAX_LEAGUE_STAT_PAGE_SIZE = "MaxLeagueStatPageSize";
    int DEFAULT_MAX_LEAGUE_STAT_PAGE_SIZE = 5000;
    String PARAM_LEAGUE_STAT_RATE_LIMIT_MINUTES = "MaxLeagueStatLimitMinutes";
    int DEFAULT_PARAM_LEAGUE_STAT_RATE_LIMIT_MINUTES = 30;
    String PARAM_LEAGUE_STAT_RATE_LIMITING_ACTIVE = "leagueStatRateLimitingActive";
    boolean DEFAULT_PARAM_LEAGUE_STAT_RATE_LIMITING_ACTIVE = false;

    // Parameters for subscription statistics
    String PARAM_MAX_SUBSCRIPTION_STAT_PAGE_SIZE = "maxSubscriptionStatPageSize";
    int DEFAULT_MAX_SUBSCRIPTION_STAT_PAGE_SIZE = 1000;
    String PARAM_SUBSCRIPTION_STAT_RATE_LIMIT_MINUTES = "maxSubscriptionStatLimitMinutes";
    int DEFAULT_PARAM_SUBSCRIPTION_STAT_RATE_LIMIT_MINUTES = 30;
    String PARAM_SUBSCRIPTION_FORCE_REFRESH_CACHE_AFTER_MINUTES = "subscriptionForceRefreshCacheAfterMinutes";
    int DEFAULT_PARAM_SUBSCRIPTION_FORCE_REFRESH_CACHE_AFTER_MINUTES = 15;
    String PARAM_SUBSCRIPTION_STAT_RATE_LIMITING_ACTIVE = "subscriptionStatRateLimitingActive";
    boolean DEFAULT_PARAM_SUBSCRIPTION_STAT_RATE_LIMITING_ACTIVE = false;

    // Parameters for old season data cleanup
    String PARAM_CLEANUP_BATCH_WAIT_TIME_MS = "cleanupBatchWaitTimeMs";
    long DEFAULT_CLEANUP_BATCH_WAIT_TIME_MS = 1000;
    String PARAM_CLEANUP_SEASON_END_BUFFER_DAYS = "cleanupSeasonEndBufferDays";
    int DEFAULT_CLEANUP_SEASON_END_BUFFER_DAYS = 15;
}
