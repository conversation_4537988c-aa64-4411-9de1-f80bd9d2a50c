-- Setup script for old season data cleanup parameter
-- This script adds the required secret key parameter to the admin.parameter table

-- Insert the deleteOldDataSecretKey parameter
-- Replace 'your-secure-secret-key-here' with a strong, unique secret key
INSERT INTO admin.parameter (id, name, value, description, created_at, modified_at, deleted, deleted_at) 
VALUES (
    gen_random_uuid(),
    'deleteOldDataSecretKey',
    'your-secure-secret-key-here',
    'Secret key required for old season data cleanup operations',
    NOW(),
    NOW(),
    false,
    NULL
) 
ON CONFLICT (name) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    modified_at = NOW(),
    modified = true;

-- Insert the cleanupSeasonEndBufferDays parameter (optional - defaults to ParameterDefaults value)
INSERT INTO admin.parameter (id, name, value, description, created_at, modified_at, deleted, deleted_at)
VALUES (
    gen_random_uuid(),
    'cleanupSeasonEndBufferDays',
    '15',
    'Number of days after season end before cleanup is allowed',
    NOW(),
    NOW(),
    false,
    NULL
)
ON CONFLICT (name) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    modified_at = NOW();

-- Insert the cleanupBatchWaitTimeMs parameter (optional - defaults to ParameterDefaults value)
INSERT INTO admin.parameter (id, name, value, description, created_at, modified_at, deleted, deleted_at)
VALUES (
    gen_random_uuid(),
    'cleanupBatchWaitTimeMs',
    '1000',
    'Wait time between batches in milliseconds during cleanup operations',
    NOW(),
    NOW(),
    false,
    NULL
)
ON CONFLICT (name) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    modified_at = NOW();

-- Verify the parameters were inserted
SELECT name, value, description, created_at
FROM admin.parameter
WHERE name IN ('deleteOldDataSecretKey', 'cleanupSeasonEndBufferDays', 'cleanupBatchWaitTimeMs')
ORDER BY name;
