package com.pass.hbl.manager.backend.persistence.service.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupRequest;
import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupResponse;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.util.BatchDeletionUtil;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;

/**
 * Service for cleaning up old season data with proper security and batch processing
 */
@Service
@Slf4j
public class OldSeasonDataCleanupService {

    @Autowired
    private HmLeagueRepository leagueRepository;

    @Autowired
    private ParameterService parameterService;

    @Autowired
    private BatchDeletionUtil batchDeletionUtil;

    private static final String SECRET_KEY_PARAM = "deleteOldDataSecretKey";
    private static final String SYSTEM_USER = ParameterDefaults.SYSTEM_USER;
    
    private static final List<String> CLEANUP_TABLES = Arrays.asList(
        "transfer_market_bid",
        "transfer_market", 
        "lineup",
        "user_round_score",
        "team",
        "league_invitation",
        "user_notification",
        "scheduler_job"
    );

    /**
     * Dry run to count records that would be deleted
     */
    @Transactional(readOnly = true)
    public DataCleanupResponse dryRunCleanup(String secretKey) {
        validateSecretKey(secretKey);

        log.info("Starting DRY RUN for old season data cleanup");

        int bufferDays = getSeasonEndBufferDays();
        List<String> previousSeasonLeaguesIds = getPreviousSeasonLeagueIds(bufferDays);
        if (CollectionUtils.isEmpty(previousSeasonLeaguesIds)) {
            return DataCleanupResponse.success(
                "No previous season found that ended " + bufferDays + "+ days ago",
                Collections.emptyMap(),
                0L,
                "00:00"
            );
        }

        Map<String, Long> recordCounts = countRecordsToDelete(previousSeasonLeaguesIds);
        long totalRecords = recordCounts.values().stream().mapToLong(Long::longValue).sum();

        log.info("DRY RUN completed. Total records to delete: {}", totalRecords);

        return DataCleanupResponse.success(
            "Dry run completed - last previous season found",
            recordCounts,
            totalRecords,
            "00:00"
        );
    }

    /**
     * Actual cleanup of old season data
     */
    @Transactional
    public DataCleanupResponse cleanupData(DataCleanupRequest request) {
        validateSecretKey(request.getSecretKey());

        log.info("Starting old season data cleanup with request: {}", request);

        int bufferDays = getSeasonEndBufferDays();
        List<String> previousSeasonLeaguesIds = getPreviousSeasonLeagueIds(bufferDays);
        if (CollectionUtils.isEmpty(previousSeasonLeaguesIds)) {
            return DataCleanupResponse.success(
                "No previous season found that ended " + bufferDays + "+ days ago",
                Collections.emptyMap(),
                0L,
                "00:00"
            );
        }

        Map<String, Long> deletedRecords = new HashMap<>();
        long startTime = System.currentTimeMillis();

        // Delete in dependency order for previous seasons only
        long waitTime = request.getWaitTimeBetweenBatchesMs() > 0 ?
                       request.getWaitTimeBetweenBatchesMs() : getBatchWaitTimeMs();

        deletedRecords.put("transfer_market_bid",
            deleteTableData("transfer_market_bid", previousSeasonLeaguesIds, request.getBatchSize(), waitTime));
        deletedRecords.put("transfer_market",
            deleteTableData("transfer_market", previousSeasonLeaguesIds, request.getBatchSize(), waitTime));
        deletedRecords.put("lineup",
            deleteTableData("lineup", previousSeasonLeaguesIds, request.getBatchSize(), waitTime));
        deletedRecords.put("user_round_score",
            deleteTableData("user_round_score", previousSeasonLeaguesIds, request.getBatchSize(), waitTime));
        deletedRecords.put("team",
            deleteTableData("team", previousSeasonLeaguesIds, request.getBatchSize(), waitTime));
        deletedRecords.put("league_invitation",
            deleteTableData("league_invitation", previousSeasonLeaguesIds, request.getBatchSize(), waitTime));
        deletedRecords.put("user_notification",
            deleteTableData("user_notification", previousSeasonLeaguesIds, request.getBatchSize(), waitTime));
        deletedRecords.put("scheduler_job",
            deleteTableData("scheduler_job", previousSeasonLeaguesIds, request.getBatchSize(), waitTime));

        long executionTime = System.currentTimeMillis() - startTime;
        long totalDeleted = deletedRecords.values().stream().mapToLong(Long::longValue).sum();

        log.info("Old season data cleanup completed. Total deleted: {}, Execution time: {}ms",
                totalDeleted, executionTime);

        return DataCleanupResponse.success(
            "Old season data cleanup completed successfully for the last previous season",
            deletedRecords,
            totalDeleted,
            formatExecutionTime(executionTime)
        );
    }

    /**
     * Validate the provided secret key against the stored parameter
     */
    private void validateSecretKey(String providedKey) {
        if (StringUtils.isBlank(providedKey)) {
            throw new IllegalArgumentException("Secret key is required");
        }

        try {
            String storedKey = parameterService.getValue(SECRET_KEY_PARAM, SYSTEM_USER);
            if (!StringUtils.equals(providedKey, storedKey)) {
                throw new SecurityException("Invalid secret key provided");
            }
        } catch (Exception e) {
            throw new IllegalStateException(SECRET_KEY_PARAM + " not configured in admin.parameter table", e);
        }
    }

    /**
     * Get batch wait time from database parameter or default
     */
    private long getBatchWaitTimeMs() {
        try {
            String value = parameterService.getValue(
                ParameterDefaults.PARAM_CLEANUP_BATCH_WAIT_TIME_MS,
                String.valueOf(ParameterDefaults.DEFAULT_CLEANUP_BATCH_WAIT_TIME_MS),
                SYSTEM_USER
            );
            return Long.parseLong(value);
        } catch (Exception e) {
            log.warn("Error getting batch wait time parameter, using default: {}", ParameterDefaults.DEFAULT_CLEANUP_BATCH_WAIT_TIME_MS);
            return ParameterDefaults.DEFAULT_CLEANUP_BATCH_WAIT_TIME_MS;
        }
    }

    /**
     * Get buffer days from database parameter or default
     */
    private int getSeasonEndBufferDays() {
        try {
            return parameterService.getAsInteger(
                ParameterDefaults.PARAM_CLEANUP_SEASON_END_BUFFER_DAYS,
                ParameterDefaults.DEFAULT_CLEANUP_SEASON_END_BUFFER_DAYS,
                SYSTEM_USER
            );
        } catch (Exception e) {
            log.warn("Error getting buffer days parameter, using default: {}", ParameterDefaults.DEFAULT_CLEANUP_SEASON_END_BUFFER_DAYS);
            return ParameterDefaults.DEFAULT_CLEANUP_SEASON_END_BUFFER_DAYS;
        }
    }

    /**
     * Get league IDs from the last previous season that ended before the buffer period
     */
    private List<String> getPreviousSeasonLeagueIds(int bufferDays) {
        LocalDate cutoffDate = LocalDate.now().minusDays(bufferDays);
        return leagueRepository.findPreviousSeasonLeaguesIds(cutoffDate);
    }

    /**
     * Count records that would be deleted for previous season leagues
     */
    private Map<String, Long> countRecordsToDelete(List<String> previousSeasonLeaguesIds) {
        Map<String, Long> counts = new HashMap<>();

        for (String tableName : CLEANUP_TABLES) {
            try {
                long count = batchDeletionUtil.countBySeasonIds(tableName, previousSeasonLeaguesIds);
                counts.put(tableName, count);
            } catch (Exception e) {
                log.error("Error counting records for table {}: {}", tableName, e.getMessage());
                counts.put(tableName, 0L);
            }
        }

        return counts;
    }

    /**
     * Delete data from a specific table
     */
    private long deleteTableData(String tableName, List<String> previousSeasonLeaguesIds, int batchSize, long waitTime) {
        try {
            log.info("Deleting {} data for previous season leagues", tableName);
            return batchDeletionUtil.deleteBySeasonIds(tableName, previousSeasonLeaguesIds, batchSize, waitTime);
        } catch (Exception e) {
            log.error("Error deleting data from table {}: {}", tableName, e.getMessage());
            return 0L;
        }
    }

    /**
     * Format execution time as HH:MM:SS or MM:SS
     */
    private String formatExecutionTime(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;

        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes % 60, seconds % 60);
        } else {
            return String.format("%02d:%02d", minutes, seconds % 60);
        }
    }
}
