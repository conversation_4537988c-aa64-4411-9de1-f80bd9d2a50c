package com.pass.hbl.manager.backend.persistence.dto.admin;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.Map;

/**
 * Response DTO for old season data cleanup operations
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataCleanupResponse {

    /**
     * Indicates if the operation was successful
     */
    private boolean success;

    /**
     * Descriptive message about the operation result
     */
    private String message;

    /**
     * Map of table names to number of records deleted/counted
     */
    private Map<String, Long> deletedRecords;

    /**
     * Total number of records deleted/counted across all tables
     */
    private long totalDeleted;

    /**
     * Execution time in formatted string (HH:MM:SS or MM:SS)
     */
    private String executionTime;

    /**
     * Creates an error response
     */
    public static DataCleanupResponse error(String message) {
        DataCleanupResponse response = new DataCleanupResponse();
        response.setSuccess(false);
        response.setMessage(message);
        response.setDeletedRecords(Collections.emptyMap());
        response.setTotalDeleted(0L);
        response.setExecutionTime("00:00");
        return response;
    }

    /**
     * Creates a success response
     */
    public static DataCleanupResponse success(String message, Map<String, Long> deletedRecords, 
                                            long totalDeleted, String executionTime) {
        return new DataCleanupResponse(true, message, deletedRecords, totalDeleted, executionTime);
    }
}
