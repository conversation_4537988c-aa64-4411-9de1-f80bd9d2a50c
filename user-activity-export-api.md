# User Activity Statistics Export API Documentation

## Overview

The User Activity Statistics Export API provides CSV exports of user activity data across different activity types. These endpoints are designed for external statistics clients to retrieve comprehensive user activity information including SSO IDs and last activity timestamps for various user actions within the handball manager system.

## Endpoints

### Lineup Activity Export
```
GET /api/v1/stat/user/lineupActivity/csv
```

### Transfer Market Activity Export
```
GET /api/v1/stat/user/transferActivity/csv
```

### Player Sales Activity Export
```
GET /api/v1/stat/user/transferActivity/sale/csv
```

### Submitted Offer Activity Export
```
GET /api/v1/stat/user/transferActivity/offer/csv
```

### League Invitation Activity Export
```
GET /api/v1/stat/user/leagueInvitation/activity/csv
```

## Authentication

These endpoints require **dual authentication**:

1. **API Key Authentication**: Include the statistics API key in the request header
2. **Magic Token Authentication**: Include the statistics magic token in the request header

### Required Headers

```
apiKey: <statistics-api-key>
magic-token: <statistics-magic-token>
```

## Request

### HTTP Method
`GET`

### Parameters
None - The endpoints automatically export data for all active users in the system.

### Example Requests

#### Lineup Activity Export
```bash
curl --output lineup_activity_statistics.csv \
  "https://hbl-sta.pass-consulting.com/api/v1/stat/user/lineupActivity/csv" \
  --header "apiKey: 01939200bf6e7d129210a75194eaeb67" \
  --header "magic-token: 9619800527" \
  --header "Accept: application/octet-stream"
```

#### Transfer Market Activity Export
```bash
curl --output transfer_market_activity_statistics.csv \
  "https://hbl-sta.pass-consulting.com/api/v1/stat/user/transferActivity/csv" \
  --header "apiKey: 01939200bf6e7d129210a75194eaeb67" \
  --header "magic-token: 9619800527" \
  --header "Accept: application/octet-stream"
```

#### Player Sales Activity Export
```bash
curl --output player_sales_activity_statistics.csv \
  "https://hbl-sta.pass-consulting.com/api/v1/stat/user/transferActivity/sale/csv" \
  --header "apiKey: 01939200bf6e7d129210a75194eaeb67" \
  --header "magic-token: 9619800527" \
  --header "Accept: application/octet-stream"
```

#### Submitted Offer Activity Export
```bash
curl --output submitted_offer_activity_statistics.csv \
  "https://hbl-sta.pass-consulting.com/api/v1/stat/user/transferActivity/offer/csv" \
  --header "apiKey: 01939200bf6e7d129210a75194eaeb67" \
  --header "magic-token: 9619800527" \
  --header "Accept: application/octet-stream"
```

#### League Invitation Activity Export
```bash
curl --output league_invitation_activity_statistics.csv \
  "https://hbl-sta.pass-consulting.com/api/v1/stat/user/leagueInvitation/activity/csv" \
  --header "apiKey: 01939200bf6e7d129210a75194eaeb67" \
  --header "magic-token: 9619800527" \
  --header "Accept: application/octet-stream"
```

## Response

### Success Response (200 OK)

**Content-Type**: `application/octet-stream`
**Content-Disposition**: `attachment; filename=<activity_type>_activity_statistics.csv`

The response is a CSV file containing user activity data with the following structure:

#### CSV Format

| Column | Type | Description |
|--------|------|-------------|
| SSO_ID | String | User's Single Sign-On identifier |
| last_activity_at | String | ISO datetime of the user's last activity (without microseconds) |

#### CSV Example

```csv
SSO_ID,last_activity_at
user123,2025-07-10T07:43:39
user456,2025-07-09T15:22:18
user789,2025-07-08T09:15:42
user101,2025-07-07T18:30:55
user102,2025-07-06T12:45:33
```

### Data Characteristics

- **Scope**: All active users in the system (`deleted = false` and `sso_id <> 'NOT_ASSIGNED'`)
- **Ordering**: Data is ordered by SSO_ID alphabetically
- **Timestamp Format**: ISO 8601 format without microseconds (e.g., `2025-07-10T07:43:39`)
- **Encoding**: UTF-8
- **Activity Types**:
  - **Lineup Activity**: Users who have created or modified lineups
  - **Transfer Market Activity**: Users who have created, modified, or deleted transfer market items
  - **Player Sales Activity**: Users who have listed players for sale on the transfer market
  - **Submitted Offer Activity**: Users who have submitted bids on transfer market items
  - **League Invitation Activity**: Users who have sent league invitations

### Error Responses

#### 401 Unauthorized
**Cause**: Missing or invalid API key or magic token.

#### 500 Internal Server Error
**Cause**: Failed to generate CSV content or database error.

## Data Sources

Each endpoint retrieves data using optimized database queries:

### Lineup Activity
- **Source**: `hm.lineup` table joined with `hm.team` and `hm.user_profile`
- **Activity Timestamp**: `MAX(lineup.created_at)` per user

### Transfer Market Activity  
- **Source**: `hm.transfer_market` table joined with `hm.user_profile`
- **Activity Timestamp**: `MAX(GREATEST(created_at, COALESCE(deleted_at, created_at)))` per user

### Player Sales Activity
- **Source**: `hm.transfer_market` table joined with `hm.user_profile`
- **Activity Timestamp**: `MAX(created_at)` per user (creation of transfer market items)

### Submitted Offer Activity
- **Source**: `hm.transfer_market_bid` table joined with `hm.user_profile`
- **Activity Timestamp**: `MAX(created_at)` per user (bid submissions)

### League Invitation Activity
- **Source**: `hm.league_invitation` table joined with `hm.user_profile`
- **Activity Timestamp**: `MAX(created_at)` per user (invitation creation)

## Performance Considerations

- All queries are optimized for large datasets using efficient JOINs and aggregation
- Only essential fields (SSO_ID and activity timestamp) are selected to minimize data transfer
- Database queries use `LEFT OUTER JOIN` to ensure all users are included
- Results are streamed directly to CSV format without intermediate object creation
- Database indexes on user_id, created_at, and deleted_at fields improve query performance

## Rate Limiting

These endpoints may be subject to rate limiting. Check with your system administrator for specific limits.

## Use Cases

- **User Engagement Analytics**: Track user activity patterns across different features
- **Retention Analysis**: Identify when users were last active in specific areas
- **Feature Usage Reporting**: Generate reports on feature adoption and usage
- **Data Migration**: Backup or transfer user activity data
- **Business Intelligence**: Feed activity data into BI tools for user behavior insights
- **Compliance Reporting**: Generate user activity reports for audit purposes

## Security Notes

- These endpoints expose user SSO IDs - ensure proper data handling compliance
- API keys and magic tokens should be kept secure and rotated regularly
- The endpoints are restricted to authorized statistics clients only
- Activity timestamps may reveal user behavior patterns - handle with appropriate privacy considerations

## Implementation Details

The endpoints follow the established pattern for CSV exports in the application:

1. **Controller Layer**: `UserStatisticsController` with separate endpoints for each activity type
2. **Service Layer**: `UserStatisticsService` with dedicated CSV generation methods
3. **Handler Layer**: `UserStatisticsActivityHandler` with optimized data retrieval and CSV formatting
4. **Repository Layer**: Optimized queries in respective repository classes
5. **Response**: Returns `ResponseEntity<ByteArrayResource>` with appropriate headers and content disposition

### Technical Stack
- **Framework**: Spring Boot with Spring Data JPA
- **Database**: PostgreSQL with optimized queries
- **CSV Generation**: Custom implementation with proper escaping and formatting
- **Authentication**: Dual authentication (API Key + Magic Token)
- **Response Format**: Streaming CSV download

## Related Endpoints

- `GET /api/v1/stat/user/details` - Get paginated user statistics data
- `GET /api/v1/stat/user/lineupActivity` - Get paginated lineup activity data (JSON)
- `GET /api/v1/stat/user/transferActivity` - Get paginated transfer activity data (JSON)
- `GET /api/v1/stat/user/transferActivity/sale` - Get paginated player sales data (JSON)
- `GET /api/v1/stat/user/transferActivity/offer` - Get paginated submitted offers data (JSON)
- `GET /api/v1/stat/user/leagueInvitation/activity` - Get paginated league invitation data (JSON)
- `GET /api/v1/statistics/league/membership/export` - Export league membership data

## Activity Type Descriptions

### Lineup Activity
Tracks when users create or modify their team lineups for matches. This includes:
- Creating new lineups for upcoming rounds
- Modifying existing lineups before round closing
- Setting player positions and formations

### Transfer Market Activity
Tracks comprehensive transfer market interactions including:
- Listing players for sale
- Removing players from the market
- Any modification to transfer market items

### Player Sales Activity
Specifically tracks when users list players for sale on the transfer market:
- Initial player listing creation
- Does not include bid-related activities

### Submitted Offer Activity
Tracks when users submit bids on transfer market items:
- Placing new bids on available players
- Does not include bid modifications or rejections

### League Invitation Activity
Tracks when users send invitations to other users to join their leagues:
- Creating new league invitations
- Inviting managers to participate in leagues

## Support

For technical support or questions about this API, contact the development team or refer to the main API documentation.

## Changelog

### Version 1.0.0
- Initial implementation of user activity CSV export endpoints
- Support for five activity types: lineup, transfer market, player sales, submitted offers, and league invitations
- Optimized database queries for performance
- Consistent CSV format across all endpoints
