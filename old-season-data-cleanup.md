# Old Season Data Cleanup Documentation

## Overview

This document outlines the implementation of the `/admin/delete-old-season-data` endpoint, which is designed to safely and efficiently delete data from previous seasons across multiple tables in the HBL Handball Manager API.

## Entity Relationship Analysis

### Tables to Clean and Their Relationships

The following tables need to be cleaned, listed in order of their dependencies (from least dependent to most dependent):

1. **scheduler_job** (hm.scheduler_job)
   - Independent table with no foreign key dependencies from other tables
   - Contains scheduled jobs that may reference old seasons

2. **league_invitation** (hm.league_invitation)
   - Foreign keys:
     - `league_id` → `hm.league.id`
     - `owner_id` → `hm.user_profile.id`
   - Contains invitations to leagues from previous seasons

3. **user_notification** (hm.user_notification)
   - Foreign keys:
     - `user_id` → `hm.user_profile.id`
   - Contains notifications that may reference old seasons

4. **user_round_score** (hm.user_round_score)
   - Foreign keys:
     - `user_id` → `hm.user_profile.id`
     - `league_id` → `hm.league.id`
     - `round_id` → `hm.round.id`
   - Contains user scores for rounds in previous seasons

5. **lineup** (hm.lineup)
   - Foreign keys:
     - `team_id` → `hm.team.id`
     - `round_id` → `hm.round.id`
   - Contains lineups for teams in previous seasons

6. **transfer_market_bid** (hm.transfer_market_bid)
   - Foreign keys:
     - `transfer_market_id` → `hm.transfer_market.id`
     - `bidder_id` → `hm.user_profile.id`
   - Contains bids for transfer market offers
   - This table has the largest volume (4M+ records)

7. **transfer_market** (hm.transfer_market)
   - Foreign keys:
     - `owner_id` → `hm.user_profile.id`
     - `league_id` → `hm.league.id`
     - `player_id` → `hm.player.id`
     - `previous_player_id` → `hm.player.id`
   - Contains transfer market offers

8. **team** (hm.team)
   - Foreign keys:
     - `owner_id` → `hm.user_profile.id`
     - `league_id` → `hm.league.id`
     - `player_id` → `hm.player.id`
   - Contains teams for users in leagues

### Deletion Order

Based on the foreign key constraints, the correct deletion order is:

1. **scheduler_job** - No dependencies
2. **league_invitation** - Depends on league
3. **user_notification** - Depends on user profile
4. **user_round_score** - Depends on user profile, league, and round
5. **lineup** - Depends on team and round
6. **transfer_market_bid** - Depends on transfer_market
7. **transfer_market** - Depends on user profile, league, and player
8. **team** - Depends on user profile, league, and player

## Implementation Approach

### 1. Service Layer

Create a dedicated service class `OldSeasonDataCleanupService` with the following components:

- Methods to identify old seasons based on configurable criteria
- Batch processing methods for each table with configurable chunk sizes
- Progress tracking and logging
- Transaction management with appropriate boundaries
- Dry-run mode to preview deletions
- Safety measures and validation

### 2. Batch Processing Strategy

For each table:

1. Identify records to delete based on season criteria
2. Process in batches (chunk size configurable, default 1000-5000)
3. Use separate transactions for each batch to avoid transaction timeouts
4. Log progress after each batch
5. Implement pause/resume capability for very large tables

### 3. Performance Optimization

- Use native SQL queries for better performance on large datasets
- Select only necessary fields in queries
- Use indexed columns in WHERE clauses
- Implement chunking with ID-based pagination for efficiency
- Consider time-based execution windows to minimize impact on system performance

### 4. Safety Measures

- Implement dry-run mode to preview deletions without making changes
- Add validation to prevent deletion of current season data
- Include configurable date thresholds (e.g., keep N most recent seasons)
- Comprehensive logging of all operations
- Transaction management to ensure data consistency

## API Endpoint Specification

### Endpoint: `/admin/delete-old-season-data`

#### Security
- Requires admin role: `ROLE_ADMIN`
- Uses existing authentication mechanisms

#### Request Parameters

```json
{
  "dryRun": true,                  // Preview mode, no actual deletions
  "seasonIds": ["uuid1", "uuid2"], // Specific seasons to clean (optional)
  "olderThanMonths": 12,           // Delete seasons older than X months (optional)
  "keepRecentSeasons": 2,          // Keep N most recent seasons (optional)
  "chunkSize": 1000,               // Batch size for processing
  "tables": [                      // Tables to process (optional, defaults to all)
    "scheduler_job",
    "league_invitation",
    "user_notification",
    "user_round_score",
    "lineup",
    "transfer_market_bid",
    "transfer_market",
    "team"
  ],
  "maxRecordsPerTable": 10000      // Maximum records to delete per table (safety limit)
}
```

#### Response

```json
{
  "jobId": "uuid",                 // ID of the scheduled job
  "status": "SCHEDULED",           // Status: SCHEDULED, RUNNING, COMPLETED, FAILED
  "dryRun": true,                  // Whether this was a dry run
  "startTime": "2023-06-01T10:00:00Z",
  "estimatedRecords": {            // Estimated records to be deleted
    "scheduler_job": 120,
    "league_invitation": 450,
    "user_notification": 12500,
    "user_round_score": 35000,
    "lineup": 28000,
    "transfer_market_bid": 4250000,
    "transfer_market": 85000,
    "team": 42000
  }
}
```

#### Status Endpoint: `/admin/delete-old-season-data/{jobId}`

```json
{
  "jobId": "uuid",
  "status": "RUNNING",
  "dryRun": true,
  "startTime": "2023-06-01T10:00:00Z",
  "currentProgress": {
    "currentTable": "transfer_market_bid",
    "totalRecords": 4250000,
    "processedRecords": 2125000,
    "percentComplete": 50,
    "elapsedTime": "00:15:30",
    "estimatedTimeRemaining": "00:15:30"
  },
  "completedTables": {
    "scheduler_job": 120,
    "league_invitation": 450,
    "user_notification": 12500,
    "user_round_score": 35000,
    "lineup": 28000
  },
  "pendingTables": [
    "transfer_market",
    "team"
  ]
}
```

## Implementation Details

### Scheduled Job Approach

The endpoint will create and schedule a job using the existing scheduler framework:

1. Create a new `OldSeasonDataCleanupJob` class extending `HmAbstractJob`
2. Implement the job to process each table in the correct order
3. Use the existing scheduler infrastructure for execution and monitoring

### Transaction Management

- Use `TransactionHandler` for managing transactions
- Implement separate transactions for each batch to avoid long-running transactions
- Use `@Transactional(propagation = Propagation.REQUIRES_NEW)` for batch operations

### Progress Tracking

- Use `ProgressLogger` for tracking and reporting progress
- Store progress information in the job entity for status reporting
- Implement pause/resume capability for handling very large tables

## Rollback Procedures

In case of issues during deletion:

1. The job will automatically stop on error and log the issue
2. Each batch is in its own transaction, so only the current batch would need rollback
3. The status endpoint will show which tables were processed and which remain
4. The job can be restarted with modified parameters to continue from where it left off

## Testing Approach

1. Create unit tests for the service layer with mock repositories
2. Implement integration tests with test data for each table
3. Test the dry-run mode to verify correct identification of records
4. Performance testing with large datasets to validate chunking approach

## Implementation Status

### Completed Components

1. **OldSeasonDataCleanupService** - Core service with batch processing and transaction management
2. **OldSeasonDataCleanupJob** - Scheduled job implementation with parameter handling
3. **DataCleanupController** - Secure admin endpoint with validation and job scheduling
4. **Request/Response DTOs** - Data transfer objects for API communication
5. **Repository Methods** - Added required count and delete methods to all repositories

### Key Features Implemented

- **Batch Processing**: Configurable chunk sizes for efficient processing
- **Transaction Management**: Separate transactions for each batch to avoid timeouts
- **Progress Tracking**: Comprehensive logging and progress reporting
- **Safety Measures**: Dry-run mode, validation, and configurable limits
- **Flexible Criteria**: Support for season IDs, age-based, or count-based cleanup
- **Secure Access**: Admin-only endpoint with proper authentication

### Usage Examples

#### Dry Run (Preview Mode)
```bash
POST /v1/admin/delete-old-season-data
{
  "dryRun": true,
  "keepRecentSeasons": 2,
  "chunkSize": 1000,
  "maxRecordsPerTable": 50000
}
```

#### Delete Specific Seasons
```bash
POST /v1/admin/delete-old-season-data
{
  "dryRun": false,
  "seasonIds": ["uuid1", "uuid2"],
  "chunkSize": 2000,
  "maxRecordsPerTable": 100000
}
```

#### Age-Based Cleanup
```bash
POST /v1/admin/delete-old-season-data
{
  "dryRun": false,
  "olderThanMonths": 18,
  "tables": ["transfer_market_bid", "transfer_market", "lineup"],
  "chunkSize": 5000,
  "maxRecordsPerTable": 1000000
}
```

### Monitoring and Status

Check job status using:
```bash
GET /v1/admin/delete-old-season-data/{jobId}
```

Monitor logs for detailed progress information and any issues during execution.
