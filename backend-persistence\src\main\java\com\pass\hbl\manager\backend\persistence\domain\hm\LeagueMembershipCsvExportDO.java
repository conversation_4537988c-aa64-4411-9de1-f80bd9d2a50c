package com.pass.hbl.manager.backend.persistence.domain.hm;

import java.time.LocalDateTime;

/**
 * Domain object for league membership CSV export data
 * Contains all fields needed for CSV export: SSO_ID, score, balance, league_id, league_name, sso_owner_id, joined_at, league_created_at, previous_league_id
 * Optimized for performance with minimal data transfer
 */
public interface LeagueMembershipCsvExportDO {

    /**
     * Get the user SSO ID
     * @return User SSO ID
     */
    String getSsoId();

    /**
     * Get the user score in the league
     * @return User score
     */
    Integer getScore();

    /**
     * Get the user balance in the league
     * @return User balance
     */
    Integer getBalance();

    /**
     * Get the league ID
     * @return League ID as string
     */
    String getLeagueId();

    /**
     * Get the league name
     * @return League name
     */
    String getLeagueName();

    /**
     * Get the league owner's SSO ID
     * @return League owner's SSO ID
     */
    String getSsoOwnerId();

    /**
     * Get the membership creation date (joined_at)
     * @return Membership creation date
     */
    LocalDateTime getJoinedAt();

    /**
     * Get the league creation date
     * @return League creation date
     */
    LocalDateTime getLeagueCreatedAt();

    /**
     * Get the previous league ID
     * @return Previous league ID as string
     */
    String getPreviousLeagueId();
}
