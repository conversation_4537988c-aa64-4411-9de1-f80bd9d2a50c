package com.pass.hbl.manager.backend.persistence.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Utility component for batch deletion operations with season-specific filtering
 */
@Component
@Slf4j
public class BatchDeletionUtil {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Delete records by season league IDs with configurable batch size and wait time
     */
    @Transactional
    public long deleteBySeasonIds(String tableName, List<String> previousSeasonLeaguesIds, int batchSize, long waitTimeMs) {
        if (CollectionUtils.isEmpty(previousSeasonLeaguesIds)) {
            log.info("No season league IDs provided for table {}, skipping deletion", tableName);
            return 0L;
        }

        if (previousSeasonLeaguesIds.size() == 1) {
            log.info("Deleting from {} for the last previous season in batches of {}", tableName, batchSize);
        } else {
            log.info("Deleting from {} for {} previous seasons in batches of {}", tableName, previousSeasonLeaguesIds.size(), batchSize);
        }

        String seasonIdsStr = previousSeasonLeaguesIds.stream()
            .map(id -> "'" + id + "'")
            .collect(Collectors.joining(","));

        String deleteQuery = buildDeleteQuery(tableName, seasonIdsStr, batchSize);
        
        int rowsAffected;
        long totalDeleted = 0;

        do {
            rowsAffected = jdbcTemplate.update(deleteQuery);
            totalDeleted += rowsAffected;
            log.info("Deleted {} rows from {}, total: {}", rowsAffected, tableName, totalDeleted);

            // Configurable pause to reduce DB load
            if (rowsAffected > 0 && waitTimeMs > 0) {
                try {
                    Thread.sleep(waitTimeMs);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Deletion interrupted for table {}", tableName);
                    break;
                }
            }
        } while (rowsAffected > 0);

        if (previousSeasonLeaguesIds.size() == 1) {
            log.info("Completed deletion from {} for the last previous season. Total rows deleted: {}", tableName, totalDeleted);
        } else {
            log.info("Completed deletion from {} for previous seasons. Total rows deleted: {}", tableName, totalDeleted);
        }
        return totalDeleted;
    }

    /**
     * Count records that would be deleted by season league IDs
     */
    public long countBySeasonIds(String tableName, List<String> previousSeasonLeaguesIds) {
        if (CollectionUtils.isEmpty(previousSeasonLeaguesIds)) {
            return 0L;
        }

        String seasonIdsStr = previousSeasonLeaguesIds.stream()
            .map(id -> "'" + id + "'")
            .collect(Collectors.joining(","));

        String countQuery = buildCountQuery(tableName, seasonIdsStr);

        try {
            Long count = jdbcTemplate.queryForObject(countQuery, Long.class);
            return count != null ? count : 0L;
        } catch (Exception e) {
            log.error("Error counting records for table {} with season leagues {}: {}", tableName, previousSeasonLeaguesIds, e.getMessage());
            return 0L;
        }
    }

    /**
     * Build appropriate DELETE query based on table structure
     */
    private String buildDeleteQuery(String tableName, String seasonIdsStr, int batchSize) {
        switch (tableName) {
            case "transfer_market_bid":
                return String.format(
                    "DELETE FROM hm.transfer_market_bid WHERE id IN (" +
                    "SELECT tmb.id FROM hm.transfer_market_bid tmb " +
                    "JOIN hm.transfer_market tm ON tmb.transfer_market_id = tm.id " +
                    "JOIN hm.league l ON tm.league_id = l.id " +
                    "WHERE l.id IN (%s) AND tmb.deleted = false LIMIT %d)", 
                    seasonIdsStr, batchSize);
                    
            case "transfer_market":
                return String.format(
                    "DELETE FROM hm.transfer_market WHERE league_id IN (%s) AND deleted = false LIMIT %d", 
                    seasonIdsStr, batchSize);
                    
            case "lineup":
                return String.format(
                    "DELETE FROM hm.lineup WHERE id IN (" +
                    "SELECT l.id FROM hm.lineup l " +
                    "JOIN hm.round r ON l.round_id = r.id " +
                    "JOIN hm.league lg ON r.league_id = lg.id " +
                    "WHERE lg.id IN (%s) AND l.deleted = false LIMIT %d)", 
                    seasonIdsStr, batchSize);
                    
            case "user_round_score":
                return String.format(
                    "DELETE FROM hm.user_round_score WHERE league_id IN (%s) AND deleted = false LIMIT %d", 
                    seasonIdsStr, batchSize);
                    
            case "team":
                return String.format(
                    "DELETE FROM hm.team WHERE league_id IN (%s) AND deleted = false LIMIT %d", 
                    seasonIdsStr, batchSize);
                    
            case "league_invitation":
                return String.format(
                    "DELETE FROM hm.league_invitation WHERE league_id IN (%s) AND deleted = false LIMIT %d", 
                    seasonIdsStr, batchSize);
                    
            case "user_notification":
                return String.format(
                    "DELETE FROM hm.user_notification WHERE league_id IN (%s) AND deleted = false LIMIT %d", 
                    seasonIdsStr, batchSize);
                    
            case "scheduler_job":
                return String.format(
                    "DELETE FROM hm.scheduler_job WHERE league_id IN (%s) AND deleted = false LIMIT %d", 
                    seasonIdsStr, batchSize);
                    
            default:
                throw new IllegalArgumentException("Unsupported table for deletion: " + tableName);
        }
    }

    /**
     * Build appropriate COUNT query based on table structure
     */
    private String buildCountQuery(String tableName, String seasonIdsStr) {
        switch (tableName) {
            case "transfer_market_bid":
                return String.format(
                    "SELECT COUNT(*) FROM hm.transfer_market_bid tmb " +
                    "JOIN hm.transfer_market tm ON tmb.transfer_market_id = tm.id " +
                    "JOIN hm.league l ON tm.league_id = l.id " +
                    "WHERE l.id IN (%s) AND tmb.deleted = false", 
                    seasonIdsStr);
                    
            case "transfer_market":
                return String.format(
                    "SELECT COUNT(*) FROM hm.transfer_market WHERE league_id IN (%s) AND deleted = false", 
                    seasonIdsStr);
                    
            case "lineup":
                return String.format(
                    "SELECT COUNT(*) FROM hm.lineup l " +
                    "JOIN hm.round r ON l.round_id = r.id " +
                    "JOIN hm.league lg ON r.league_id = lg.id " +
                    "WHERE lg.id IN (%s) AND l.deleted = false", 
                    seasonIdsStr);
                    
            case "user_round_score":
                return String.format(
                    "SELECT COUNT(*) FROM hm.user_round_score WHERE league_id IN (%s) AND deleted = false", 
                    seasonIdsStr);
                    
            case "team":
                return String.format(
                    "SELECT COUNT(*) FROM hm.team WHERE league_id IN (%s) AND deleted = false", 
                    seasonIdsStr);
                    
            case "league_invitation":
                return String.format(
                    "SELECT COUNT(*) FROM hm.league_invitation WHERE league_id IN (%s) AND deleted = false", 
                    seasonIdsStr);
                    
            case "user_notification":
                return String.format(
                    "SELECT COUNT(*) FROM hm.user_notification WHERE league_id IN (%s) AND deleted = false", 
                    seasonIdsStr);
                    
            case "scheduler_job":
                return String.format(
                    "SELECT COUNT(*) FROM hm.scheduler_job WHERE league_id IN (%s) AND deleted = false", 
                    seasonIdsStr);
                    
            default:
                throw new IllegalArgumentException("Unsupported table for counting: " + tableName);
        }
    }
}
