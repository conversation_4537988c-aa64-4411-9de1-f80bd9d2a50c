package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupRequest;
import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupResponse;
import com.pass.hbl.manager.backend.persistence.service.admin.OldSeasonDataCleanupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * Controller for old season data cleanup operations
 */
@RestController
@RequestMapping("/" + ApiConstants.VERSION + "/data-cleanup")
@Validated
@Tag(name = "dataCleanup", description = "Old season data cleanup operations")
@RolesAllowed({ApiConstants.ROLE_ADMIN})
@Slf4j
public class DataCleanupController {

    @Autowired
    private OldSeasonDataCleanupService cleanupService;

    /**
     * Dry run endpoint to preview what would be deleted
     */
    @Operation(
        summary = "Dry run for old season data cleanup",
        description = "Preview what data would be deleted without performing actual deletions. " +
                     "Returns count of records that would be deleted from each table for previous seasons " +
                     "that ended 15+ days ago."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Dry run completed successfully",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = DataCleanupResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "403", 
            description = "Invalid secret key or insufficient permissions"
        ),
        @ApiResponse(
            responseCode = "500", 
            description = "Internal server error during dry run"
        )
    })
    @GetMapping(value = "/delete-old-season-data/dry-run", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<DataCleanupResponse> dryRunDeleteOldSeasonData(
            @Parameter(
                name = "secretKey", 
                description = "Secret key for authorization", 
                required = true
            )
            @RequestParam("secretKey") 
            @NotBlank(message = "Secret key is required") 
            String secretKey) {

        log.info("Starting dry run for old season data cleanup");

        try {
            DataCleanupResponse response = cleanupService.dryRunCleanup(secretKey);
            return ResponseEntity.ok(response);
        } catch (SecurityException e) {
            log.error("Security error during dry run: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(DataCleanupResponse.error("Invalid secret key"));
        } catch (IllegalArgumentException e) {
            log.error("Invalid argument during dry run: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(DataCleanupResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Error during dry run", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(DataCleanupResponse.error("Internal server error: " + e.getMessage()));
        }
    }

    /**
     * Actual deletion endpoint
     */
    @Operation(
        summary = "Delete old season data",
        description = "Perform actual deletion of old season data for previous seasons that ended 15+ days ago. " +
                     "Deletes data from all related tables in proper dependency order with configurable batch processing."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Data cleanup completed successfully",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = DataCleanupResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "400", 
            description = "Invalid request parameters"
        ),
        @ApiResponse(
            responseCode = "403", 
            description = "Invalid secret key or insufficient permissions"
        ),
        @ApiResponse(
            responseCode = "500", 
            description = "Internal server error during cleanup"
        )
    })
    @PostMapping(
        value = "/delete-old-season-data", 
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    public ResponseEntity<DataCleanupResponse> deleteOldSeasonData(
            @Parameter(
                name = "request", 
                description = "Data cleanup request with secret key and configuration", 
                required = true
            )
            @RequestBody 
            @Valid 
            DataCleanupRequest request) {

        log.info("Starting old season data cleanup with request: {}", request);

        try {
            // Additional validation
            if (!request.isValid()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(DataCleanupResponse.error("Invalid request parameters"));
            }

            DataCleanupResponse response = cleanupService.cleanupData(request);
            return ResponseEntity.ok(response);
        } catch (SecurityException e) {
            log.error("Security error during cleanup: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(DataCleanupResponse.error("Invalid secret key"));
        } catch (IllegalArgumentException e) {
            log.error("Invalid argument during cleanup: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(DataCleanupResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Error during data cleanup", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(DataCleanupResponse.error("Internal server error: " + e.getMessage()));
        }
    }
}
