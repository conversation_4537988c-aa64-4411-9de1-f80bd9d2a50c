package com.pass.hbl.manager.backend.persistence.dto.hm;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(implementation = AwardCode.class)
public enum AwardCode {
    LEAGUE_WINNER,
    FIRST_TRANSFER,
    MATC<PERSON>AY_WINNER,
    PICKER_BRONZE,
    PICKER_SILVER,
    PICKER_GOLD,
    DEALMAKER_BRONZE,
    DEALMAKER_SILVER,
    DEALMAKER_GOLD,
    FULL_HOUSE,
    TKING_BRONZE,
    TKING_SILVER,
    TKING_GOLD,
    START7,
    FULL_BENCH,
    IMA<PERSON>_RIGHT,
    MATCHDAY_HATRICK,
    SQUAD_VALUE
}